2025-08-05 21:49:31 | INFO     | main:lifespan:33 - Starting LLM-Powered Query-Retrieval System
2025-08-05 21:49:31 | INFO     | services.vector_store:initialize:29 - Initializing vector store...
2025-08-05 21:49:31 | INFO     | services.embedding_service:initialize:33 - Initializing embedding service with provider: gemini
2025-08-05 21:49:31 | INFO     | services.embedding_service:_initialize_gemini:61 - Using Gemini model: models/embedding-001
2025-08-05 21:49:31 | INFO     | services.embedding_service:initialize:47 - Embedding service initialized with dimension: 768
2025-08-05 21:49:31 | INFO     | services.vector_store:_load_index:198 - No existing vector store found, starting fresh
2025-08-05 21:49:31 | INFO     | services.vector_store:initialize:42 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-05 21:49:31 | INFO     | main:lifespan:41 - System initialized successfully
