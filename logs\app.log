2025-08-05 21:49:31 | INFO     | main:lifespan:33 - Starting LLM-Powered Query-Retrieval System
2025-08-05 21:49:31 | INFO     | services.vector_store:initialize:29 - Initializing vector store...
2025-08-05 21:49:31 | INFO     | services.embedding_service:initialize:33 - Initializing embedding service with provider: gemini
2025-08-05 21:49:31 | INFO     | services.embedding_service:_initialize_gemini:61 - Using Gemini model: models/embedding-001
2025-08-05 21:49:31 | INFO     | services.embedding_service:initialize:47 - Embedding service initialized with dimension: 768
2025-08-05 21:49:31 | INFO     | services.vector_store:_load_index:198 - No existing vector store found, starting fresh
2025-08-05 21:49:31 | INFO     | services.vector_store:initialize:42 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-05 21:49:31 | INFO     | main:lifespan:41 - System initialized successfully
2025-08-05 21:58:46 | INFO     | main:run_query:103 - Processing request with 10 questions
2025-08-05 21:58:46 | INFO     | services.document_processor:process_document:37 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-05 21:58:47 | INFO     | services.document_processor:process_document:60 - Successfully extracted 109014 characters from document
2025-08-05 21:58:47 | INFO     | services.document_processor:chunk_text:204 - Created 273 chunks from text
2025-08-05 21:58:47 | INFO     | services.vector_store:add_documents:60 - Adding 273 chunks to vector store
2025-08-05 22:01:43 | INFO     | services.vector_store:_save_index:177 - Vector store saved to disk
2025-08-05 22:01:43 | INFO     | services.vector_store:add_documents:83 - Successfully added 273 chunks to vector store
2025-08-05 22:01:43 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?...'
2025-08-05 22:01:43 | INFO     | services.vector_store:search:105 - Searching for query: 'the grace period for premium payment under the Nat...' with top_k=3
2025-08-05 22:01:43 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:43 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1102 characters from 3 chunks
2025-08-05 22:01:43 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:43 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:43 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases (PED) to be covered?...'
2025-08-05 22:01:43 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for pre-existing diseases (PED)...' with top_k=3
2025-08-05 22:01:44 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:44 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1359 characters from 3 chunks
2025-08-05 22:01:44 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses, and what are the conditions?...'
2025-08-05 22:01:44 | INFO     | services.vector_store:search:105 - Searching for query: 'this policy cover maternity expenses, and the cond...' with top_k=3
2025-08-05 22:01:44 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:44 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1285 characters from 3 chunks
2025-08-05 22:01:44 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-05 22:01:44 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=3
2025-08-05 22:01:45 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:45 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1341 characters from 3 chunks
2025-08-05 22:01:45 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are the medical expenses for an organ donor covered under this policy?...'
2025-08-05 22:01:45 | INFO     | services.vector_store:search:105 - Searching for query: 'the medical expenses for an organ donor covered un...' with top_k=3
2025-08-05 22:01:45 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:45 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1382 characters from 3 chunks
2025-08-05 22:01:45 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the No Claim Discount (NCD) offered in this policy?...'
2025-08-05 22:01:45 | INFO     | services.vector_store:search:105 - Searching for query: 'the No Claim Discount (NCD) offered in this policy...' with top_k=3
2025-08-05 22:01:46 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:46 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1378 characters from 3 chunks
2025-08-05 22:01:46 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-05 22:01:46 | INFO     | services.vector_store:search:105 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=3
2025-08-05 22:01:46 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:46 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1294 characters from 3 chunks
2025-08-05 22:01:46 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-05 22:01:46 | INFO     | services.vector_store:search:105 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=3
2025-08-05 22:01:47 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:47 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1534 characters from 3 chunks
2025-08-05 22:01:47 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-05 22:01:47 | INFO     | services.vector_store:search:105 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=3
2025-08-05 22:01:47 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:47 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1348 characters from 3 chunks
2025-08-05 22:01:47 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are there any sub-limits on room rent and ICU charges for Plan A?...'
2025-08-05 22:01:47 | INFO     | services.vector_store:search:105 - Searching for query: 'there any sub-limits on room rent and ICU charges ...' with top_k=3
2025-08-05 22:01:48 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:48 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 2309 characters from 3 chunks
2025-08-05 22:01:48 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:48 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:48 | INFO     | main:run_query:118 - Successfully processed 10 questions
2025-08-05 22:12:18 | INFO     | main:run_query:103 - Processing request with 6 questions
2025-08-05 22:12:18 | INFO     | services.document_processor:process_document:37 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-05 22:12:19 | INFO     | services.document_processor:process_document:60 - Successfully extracted 109014 characters from document
2025-08-05 22:12:19 | INFO     | services.document_processor:chunk_text:204 - Created 273 chunks from text
2025-08-05 22:12:19 | INFO     | services.vector_store:add_documents:60 - Adding 273 chunks to vector store
2025-08-05 22:15:19 | INFO     | services.vector_store:_save_index:177 - Vector store saved to disk
2025-08-05 22:15:19 | INFO     | services.vector_store:add_documents:83 - Successfully added 273 chunks to vector store
2025-08-05 22:15:19 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment?...'
2025-08-05 22:15:19 | INFO     | services.vector_store:search:105 - Searching for query: 'the grace period for premium payment? premium grac...' with top_k=3
2025-08-05 22:15:19 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:19 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1128 characters from 3 chunks
2025-08-05 22:15:19 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:19 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:19 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases?...'
2025-08-05 22:15:19 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for pre-existing diseases? wait...' with top_k=3
2025-08-05 22:15:20 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:20 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1380 characters from 3 chunks
2025-08-05 22:15:20 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses?...'
2025-08-05 22:15:20 | INFO     | services.vector_store:search:105 - Searching for query: 'this policy cover maternity expenses? policy mater...' with top_k=3
2025-08-05 22:15:20 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:20 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1285 characters from 3 chunks
2025-08-05 22:15:20 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-05 22:15:20 | INFO     | services.vector_store:search:105 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=3
2025-08-05 22:15:21 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:21 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1294 characters from 3 chunks
2025-08-05 22:15:21 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-05 22:15:21 | INFO     | services.vector_store:search:105 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=3
2025-08-05 22:15:21 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:21 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1534 characters from 3 chunks
2025-08-05 22:15:21 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-05 22:15:21 | INFO     | services.vector_store:search:105 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=3
2025-08-05 22:15:22 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:22 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1348 characters from 3 chunks
2025-08-05 22:15:22 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:22 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:22 | INFO     | main:run_query:118 - Successfully processed 6 questions
2025-08-05 22:17:40 | INFO     | main:lifespan:46 - Shutting down system
2025-08-05 22:17:48 | INFO     | main:lifespan:33 - Starting LLM-Powered Query-Retrieval System
2025-08-05 22:17:48 | INFO     | services.vector_store:initialize:29 - Initializing vector store...
2025-08-05 22:17:48 | INFO     | services.embedding_service:initialize:33 - Initializing embedding service with provider: gemini
2025-08-05 22:17:49 | INFO     | services.embedding_service:_initialize_gemini:61 - Using Gemini model: models/embedding-001
2025-08-05 22:17:49 | INFO     | services.embedding_service:initialize:47 - Embedding service initialized with dimension: 768
2025-08-05 22:17:49 | INFO     | services.vector_store:_load_index:196 - Loaded vector store with 273 chunks
2025-08-05 22:17:49 | INFO     | services.vector_store:initialize:42 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-05 22:17:49 | INFO     | main:lifespan:41 - System initialized successfully
2025-08-05 22:18:18 | INFO     | main:lifespan:46 - Shutting down system
