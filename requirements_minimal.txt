# Minimal requirements - Gemini only (no sentence-transformers)
fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6
pydantic==2.5.0
requests==2.31.0
httpx==0.25.2

# Document processing
PyMuPDF==1.23.8
python-docx==1.1.0

# Vector search (minimal)
faiss-cpu==1.7.4
numpy==1.24.3

# Gemini AI
google-generativeai==0.3.2

# Utilities
python-dotenv==1.0.0
pydantic-settings==2.1.0
loguru==0.7.2
tqdm==4.66.1
aiofiles==23.2.0
