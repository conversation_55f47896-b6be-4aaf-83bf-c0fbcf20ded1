"""
Document processing service for extracting text from PDFs and DOCX files
"""
import os
import re
import tempfile
from typing import List, Dict, Any
import requests
import fitz  # PyMuPDF
from docx import Document
from loguru import logger
from urllib.parse import urlparse

from config import settings
from models.schemas import DocumentChunk


class DocumentProcessor:
    """Service for processing various document formats"""
    
    def __init__(self):
        self.supported_formats = ['.pdf', '.docx', '.doc']
        self.chunk_size = settings.chunk_size
        self.chunk_overlap = settings.chunk_overlap
    
    async def process_document(self, document_url: str) -> str:
        """
        Download and process a document from URL
        
        Args:
            document_url: URL to the document
            
        Returns:
            Extracted text content
        """
        try:
            logger.info(f"Processing document from URL: {document_url}")
            
            # Download the document
            response = requests.get(document_url, timeout=30)
            response.raise_for_status()
            
            # Determine file type from URL or content-type
            file_extension = self._get_file_extension(document_url, response.headers)
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=file_extension, delete=False) as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name
            
            try:
                # Extract text based on file type
                if file_extension.lower() == '.pdf':
                    text = self._extract_text_from_pdf(temp_file_path)
                elif file_extension.lower() in ['.docx', '.doc']:
                    text = self._extract_text_from_docx(temp_file_path)
                else:
                    raise ValueError(f"Unsupported file format: {file_extension}")
                
                logger.info(f"Successfully extracted {len(text)} characters from document")
                return text
                
            finally:
                # Clean up temporary file
                os.unlink(temp_file_path)
                
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            raise
    
    def _get_file_extension(self, url: str, headers: Dict[str, str]) -> str:
        """Determine file extension from URL or content-type header"""
        # Try to get extension from URL
        parsed_url = urlparse(url)
        path = parsed_url.path
        if path:
            _, ext = os.path.splitext(path)
            if ext.lower() in self.supported_formats:
                return ext
        
        # Try to get from content-type header
        content_type = headers.get('content-type', '').lower()
        if 'pdf' in content_type:
            return '.pdf'
        elif 'word' in content_type or 'officedocument' in content_type:
            return '.docx'
        
        # Default to PDF if uncertain
        return '.pdf'
    
    def _extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file using PyMuPDF"""
        try:
            doc = fitz.open(file_path)
            text = ""
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                page_text = page.get_text()
                text += f"\n--- Page {page_num + 1} ---\n{page_text}"
            
            doc.close()
            return self._clean_text(text)
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            raise
    
    def _extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
            
            return self._clean_text(text)
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {str(e)}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep punctuation
        text = re.sub(r'[^\w\s\.\,\;\:\!\?\-\(\)\[\]\"\'\/\%\$\@\#]', '', text)
        
        # Normalize line breaks
        text = re.sub(r'\n+', '\n', text)
        
        return text.strip()
    
    async def chunk_text(self, text: str) -> List[DocumentChunk]:
        """
        Split text into chunks for vector embedding
        
        Args:
            text: Input text to chunk
            
        Returns:
            List of DocumentChunk objects
        """
        try:
            chunks = []
            
            # Split text into sentences first
            sentences = self._split_into_sentences(text)
            
            current_chunk = ""
            chunk_id = 0
            
            for sentence in sentences:
                # Check if adding this sentence would exceed chunk size
                if len(current_chunk) + len(sentence) > self.chunk_size:
                    if current_chunk:
                        # Create chunk
                        chunk = DocumentChunk(
                            id=f"chunk_{chunk_id}",
                            content=current_chunk.strip(),
                            metadata={
                                "chunk_index": chunk_id,
                                "length": len(current_chunk),
                                "sentence_count": len(current_chunk.split('.'))
                            }
                        )
                        chunks.append(chunk)
                        chunk_id += 1
                        
                        # Start new chunk with overlap
                        if self.chunk_overlap > 0:
                            overlap_text = current_chunk[-self.chunk_overlap:]
                            current_chunk = overlap_text + " " + sentence
                        else:
                            current_chunk = sentence
                    else:
                        current_chunk = sentence
                else:
                    current_chunk += " " + sentence if current_chunk else sentence
            
            # Add the last chunk
            if current_chunk:
                chunk = DocumentChunk(
                    id=f"chunk_{chunk_id}",
                    content=current_chunk.strip(),
                    metadata={
                        "chunk_index": chunk_id,
                        "length": len(current_chunk),
                        "sentence_count": len(current_chunk.split('.'))
                    }
                )
                chunks.append(chunk)
            
            logger.info(f"Created {len(chunks)} chunks from text")
            return chunks
            
        except Exception as e:
            logger.error(f"Error chunking text: {str(e)}")
            raise
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences using simple regex"""
        # Simple sentence splitting - can be improved with NLTK
        sentences = re.split(r'(?<=[.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]
