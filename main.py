"""
Main FastAPI application for LLM-Powered Query-Retrieval System
"""
import os
import sys
from fastapi import FastAPI, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import uvicorn
from loguru import logger

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings
from services.document_processor import DocumentProcessor
from services.vector_store import VectorStore
from services.query_processor import QueryProcessor
from models.schemas import QueryRequest, QueryResponse
from utils.logger import setup_logging

# Initialize FastAPI app
app = FastAPI(
    title="LLM-Powered Query-Retrieval System",
    description="Intelligent document processing and query answering system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Initialize services
document_processor = DocumentProcessor()
vector_store = VectorStore()
query_processor = QueryProcessor(vector_store)

def verify_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify the bearer token"""
    if credentials.credentials != settings.api_bearer_token:
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    return credentials.credentials

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    # Setup logging
    setup_logging()
    logger.info("Starting LLM-Powered Query-Retrieval System")
    await vector_store.initialize()
    logger.info("System initialized successfully")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "LLM-Powered Query-Retrieval System is running"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "services": {
            "document_processor": "ready",
            "vector_store": "ready",
            "query_processor": "ready"
        }
    }

@app.post("/hackrx/run", response_model=QueryResponse)
async def run_query(
    request: QueryRequest,
    token: str = Depends(verify_token)
):
    """
    Main endpoint for processing documents and answering queries
    """
    try:
        logger.info(f"Processing request with {len(request.questions)} questions")
        
        # Process the document
        document_text = await document_processor.process_document(request.documents)
        
        # Create embeddings and store in vector database
        chunks = await document_processor.chunk_text(document_text)
        await vector_store.add_documents(chunks)
        
        # Process queries and generate answers
        answers = []
        for question in request.questions:
            answer = await query_processor.process_query(question, document_text)
            answers.append(answer)
        
        logger.info(f"Successfully processed {len(answers)} questions")
        return QueryResponse(answers=answers)
        
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True,
        log_level=settings.log_level.lower()
    )
