"""
Embedding service supporting both Gemini and sentence-transformers
"""
import numpy as np
from typing import List, Union
from loguru import logger
import google.generativeai as genai
from sentence_transformers import SentenceTransformer

from config import settings


class EmbeddingService:
    """Service for generating embeddings using different providers"""
    
    def __init__(self):
        self.provider = settings.embedding_provider
        self.gemini_model = None
        self.sentence_transformer_model = None
        self.dimension = settings.vector_dimension
        
    async def initialize(self):
        """Initialize the embedding service based on the configured provider"""
        try:
            logger.info(f"Initializing embedding service with provider: {self.provider}")
            
            if self.provider == "gemini":
                await self._initialize_gemini()
            elif self.provider == "sentence-transformers":
                await self._initialize_sentence_transformers()
            else:
                raise ValueError(f"Unsupported embedding provider: {self.provider}")
            
            logger.info(f"Embedding service initialized with dimension: {self.dimension}")
            
        except Exception as e:
            logger.error(f"Error initializing embedding service: {str(e)}")
            raise
    
    async def _initialize_gemini(self):
        """Initialize Gemini embedding service"""
        try:
            # Configure Gemini API
            genai.configure(api_key=settings.gemini_api_key)
            
            # Test the connection and get model info
            model_info = genai.get_model(settings.gemini_embedding_model)
            logger.info(f"Using Gemini model: {model_info.name}")
            
            # Gemini embedding-001 produces 768-dimensional embeddings
            self.dimension = 768
            
        except Exception as e:
            logger.error(f"Error initializing Gemini: {str(e)}")
            raise
    
    async def _initialize_sentence_transformers(self):
        """Initialize sentence-transformers embedding service"""
        try:
            self.sentence_transformer_model = SentenceTransformer(settings.embedding_model)
            self.dimension = self.sentence_transformer_model.get_sentence_embedding_dimension()
            
        except Exception as e:
            logger.error(f"Error initializing sentence-transformers: {str(e)}")
            raise
    
    async def encode(self, texts: Union[str, List[str]], normalize: bool = True) -> np.ndarray:
        """
        Generate embeddings for the given texts
        
        Args:
            texts: Single text or list of texts to embed
            normalize: Whether to normalize embeddings for cosine similarity
            
        Returns:
            Numpy array of embeddings
        """
        try:
            if isinstance(texts, str):
                texts = [texts]
            
            if self.provider == "gemini":
                embeddings = await self._encode_with_gemini(texts)
            elif self.provider == "sentence-transformers":
                embeddings = await self._encode_with_sentence_transformers(texts, normalize)
            else:
                raise ValueError(f"Unsupported provider: {self.provider}")
            
            logger.debug(f"Generated embeddings for {len(texts)} texts with shape: {embeddings.shape}")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise
    
    async def _encode_with_gemini(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings using Gemini"""
        try:
            embeddings = []
            
            for text in texts:
                # Generate embedding using Gemini
                result = genai.embed_content(
                    model=settings.gemini_embedding_model,
                    content=text,
                    task_type="retrieval_document"  # Optimized for document retrieval
                )
                
                embedding = np.array(result['embedding'], dtype=np.float32)
                embeddings.append(embedding)
            
            embeddings_array = np.vstack(embeddings)
            
            # Normalize for cosine similarity
            norms = np.linalg.norm(embeddings_array, axis=1, keepdims=True)
            embeddings_array = embeddings_array / (norms + 1e-8)  # Add small epsilon to avoid division by zero
            
            return embeddings_array
            
        except Exception as e:
            logger.error(f"Error with Gemini embeddings: {str(e)}")
            raise
    
    async def _encode_with_sentence_transformers(self, texts: List[str], normalize: bool = True) -> np.ndarray:
        """Generate embeddings using sentence-transformers"""
        try:
            embeddings = self.sentence_transformer_model.encode(
                texts,
                convert_to_numpy=True,
                normalize_embeddings=normalize
            )
            
            return embeddings.astype(np.float32)
            
        except Exception as e:
            logger.error(f"Error with sentence-transformers embeddings: {str(e)}")
            raise
    
    async def encode_query(self, query: str) -> np.ndarray:
        """
        Generate embedding for a query (optimized for search)
        
        Args:
            query: Query text
            
        Returns:
            Query embedding
        """
        try:
            if self.provider == "gemini":
                # Use retrieval_query task type for queries
                result = genai.embed_content(
                    model=settings.gemini_embedding_model,
                    content=query,
                    task_type="retrieval_query"  # Optimized for query retrieval
                )
                
                embedding = np.array(result['embedding'], dtype=np.float32)
                
                # Normalize for cosine similarity
                norm = np.linalg.norm(embedding)
                embedding = embedding / (norm + 1e-8)
                
                return embedding.reshape(1, -1)  # Return as 2D array
            else:
                # Use regular encoding for sentence-transformers
                return await self.encode(query, normalize=True)
                
        except Exception as e:
            logger.error(f"Error generating query embedding: {str(e)}")
            raise
    
    def get_dimension(self) -> int:
        """Get the embedding dimension"""
        return self.dimension
    
    def get_provider_info(self) -> dict:
        """Get information about the current provider"""
        return {
            "provider": self.provider,
            "dimension": self.dimension,
            "model": (
                settings.gemini_embedding_model if self.provider == "gemini" 
                else settings.embedding_model
            )
        }
