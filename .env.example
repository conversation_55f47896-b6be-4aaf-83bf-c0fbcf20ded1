# Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_BEARER_TOKEN=87e1f87355284c19bac1880413d4a1e7cb868891939fc1c6d8227ee2c1b89cb0

# Vector Database Configuration
VECTOR_DIMENSION=768
FAISS_INDEX_PATH=./data/faiss_index
CHUNK_SIZE=512
CHUNK_OVERLAP=50

# Model Configuration
EMBEDDING_PROVIDER=gemini
EMBEDDING_MODEL=all-MiniLM-L6-v2
GEMINI_EMBEDDING_MODEL=models/embedding-001
GEMINI_MODEL=gemini-1.5-flash
MAX_TOKENS=4000
TEMPERATURE=0.1

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Cache Configuration
ENABLE_CACHE=true
CACHE_TTL=3600
