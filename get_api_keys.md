# API Keys Setup Guide

This guide will help you obtain the required API keys for the LLM-Powered Query-Retrieval System.

## 🔑 Required API Keys

### 1. Google Gemini API Key (for Embeddings)

**Step 1**: Visit Google AI Studio
- Go to [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
- Sign in with your Google account

**Step 2**: Create API Key
- Click "Create API Key"
- Select your Google Cloud project (or create a new one)
- Copy the generated API key

**Step 3**: Add to .env file
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

### 2. OpenAI API Key (for LLM Responses)

**Step 1**: Visit OpenAI Platform
- Go to [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
- Sign in or create an OpenAI account

**Step 2**: Create API Key
- Click "Create new secret key"
- Give it a name (e.g., "HackRX Project")
- Copy the generated API key (starts with "sk-")

**Step 3**: Add to .env file
```env
OPENAI_API_KEY=your_openai_api_key_here
```

## 💰 Cost Considerations

### Gemini API
- **Free Tier**: 60 requests per minute
- **Paid Tier**: $0.0001 per 1K characters for embedding-001
- Very cost-effective for embeddings

### OpenAI API
- **GPT-4 Turbo**: ~$0.01 per 1K input tokens, ~$0.03 per 1K output tokens
- Consider using GPT-3.5-turbo for lower costs during development

## 🔧 Configuration Options

### Use Sentence-Transformers Instead of Gemini (Free)

If you prefer not to use Gemini, you can use sentence-transformers (runs locally):

```env
EMBEDDING_PROVIDER=sentence-transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2
VECTOR_DIMENSION=384
```

### Alternative LLM Models

You can also use different OpenAI models:

```env
LLM_MODEL=gpt-3.5-turbo  # Cheaper option
# or
LLM_MODEL=gpt-4-turbo-preview  # Better quality
```

## 🚀 Quick Setup

1. Copy `.env.example` to `.env`
2. Add your API keys to the `.env` file
3. Run `python run.py` to start the system

## 🔒 Security Notes

- Never commit your `.env` file to version control
- Keep your API keys secure and don't share them
- Consider using environment variables in production
- Monitor your API usage to avoid unexpected charges

## 🆘 Need Help?

- **Gemini API Issues**: Check [Google AI Studio Documentation](https://ai.google.dev/docs)
- **OpenAI API Issues**: Check [OpenAI Platform Documentation](https://platform.openai.com/docs)
- **Billing Questions**: Check respective platform billing sections

---

**Ready to go?** Run `python run.py` to start the system!
