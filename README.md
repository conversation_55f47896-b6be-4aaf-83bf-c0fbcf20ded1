# LLM-Powered Query-Retrieval System

An intelligent document processing and query answering system designed for insurance, legal, HR, and compliance domains.

## 🚀 Features

- **Multi-format Document Processing**: Supports PDF and DOCX files
- **Gemini AI Integration**: Uses Google Gemini for both embeddings and text generation
- **Semantic Search**: FAISS vector database for efficient similarity search
- **Intelligent Responses**: Powered by Gemini 1.5 Flash for contextual understanding
- **RESTful API**: FastAPI-based backend with comprehensive documentation
- **Explainable AI**: Provides reasoning and confidence scores for answers
- **Cost-Effective**: Single API for all AI operations

## 🏗️ System Architecture

```
Input Documents (PDF/DOCX) → Document Processor → Text Chunking →
Gemini Vector Embeddings → FAISS Vector Store → Semantic Search →
Gemini 1.5 Flash Processing → Structured JSON Response
```

## 📋 Requirements

- Python 3.8+
- Google Gemini API Key (for embeddings and text generation)
- 2GB+ RAM (for processing)
- Internet connection (for document download and API calls)

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd hackRX-bajaj
```

### 2. Install Dependencies and Run

```bash
python run.py
```

This will:
- Install all required dependencies
- Create necessary directories
- Set up environment configuration
- Start the FastAPI server

### 3. Configure API Key

Edit the `.env` file and add your Gemini API key:

```env
GEMINI_API_KEY=your_gemini_api_key_here
EMBEDDING_PROVIDER=gemini
GEMINI_MODEL=gemini-1.5-flash
```

### 4. Access the API

- **Server**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 📡 API Usage

### Main Endpoint

**POST** `/hackrx/run`

```json
{
    "documents": "https://example.com/document.pdf",
    "questions": [
        "What is the grace period for premium payment?",
        "What is the waiting period for pre-existing diseases?"
    ]
}
```

**Response:**

```json
{
    "answers": [
        "The grace period for premium payment is 30 days.",
        "The waiting period for pre-existing diseases is 36 months."
    ]
}
```

### Authentication

Include the bearer token in your requests:

```bash
Authorization: Bearer 87e1f87355284c19bac1880413d4a1e7cb868891939fc1c6d8227ee2c1b89cb0
```

## 🧪 Testing

Run the test suites:

```bash
# Test Gemini embeddings specifically
python test_gemini.py

# Test the complete system
python test_system.py

# Test the API endpoints
python test_api.py
```

This will test:
- Gemini embedding functionality
- Document processing pipeline
- Vector store operations
- Health check endpoint
- Query answering functionality

## 📁 Project Structure

```
├── main.py                 # FastAPI application
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── run.py                 # Startup script
├── test_api.py           # API testing script
├── models/
│   └── schemas.py        # Pydantic models
├── services/
│   ├── document_processor.py  # Document processing
│   ├── vector_store.py        # FAISS vector operations
│   └── query_processor.py     # Query processing & LLM
└── utils/
    └── logger.py         # Logging configuration
```

## ⚙️ Configuration

Key configuration options in `.env`:

```env
# Gemini API Configuration
GEMINI_API_KEY=your_gemini_key_here

# Model Configuration
EMBEDDING_PROVIDER=gemini
GEMINI_EMBEDDING_MODEL=models/embedding-001
GEMINI_MODEL=gemini-1.5-flash

# Vector Database
VECTOR_DIMENSION=768
CHUNK_SIZE=512
CHUNK_OVERLAP=50

# API Settings
API_PORT=8000
API_BEARER_TOKEN=your_token_here
```

## 🎯 Evaluation Criteria

The system is optimized for:

- **Accuracy**: Precise query understanding and clause matching
- **Token Efficiency**: Optimized LLM usage for cost-effectiveness
- **Latency**: Fast response times with efficient vector search
- **Reusability**: Modular architecture for easy extension
- **Explainability**: Clear reasoning and source traceability

## 🔧 Advanced Usage

### Custom Document Processing

```python
from services.document_processor import DocumentProcessor

processor = DocumentProcessor()
text = await processor.process_document("document_url")
chunks = await processor.chunk_text(text)
```

### Gemini Embeddings

```python
from services.embedding_service import EmbeddingService

embedding_service = EmbeddingService()
await embedding_service.initialize()
embeddings = await embedding_service.encode(["your text"])
```

### Vector Search

```python
from services.vector_store import VectorStore

vector_store = VectorStore()
await vector_store.initialize()
results = await vector_store.search("your query", top_k=5)
```

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**: Run `python run.py` to install dependencies
2. **API Key Issues**: Ensure GEMINI_API_KEY is set in `.env`
3. **Gemini Issues**: Check API key validity and internet connection
4. **Memory Issues**: Reduce CHUNK_SIZE in configuration
5. **Network Issues**: Check document URL accessibility

### Logs

Check logs in `./logs/app.log` for detailed error information.

## 📄 License

This project is developed for the HackRX competition.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

---

**Built with ❤️ for HackRX Bajaj Challenge**
