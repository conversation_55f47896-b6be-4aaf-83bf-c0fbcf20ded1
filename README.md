# LLM-Powered Query-Retrieval System

An intelligent document processing and query answering system designed for insurance, legal, HR, and compliance domains.

## 🚀 Features

- **Multi-format Document Processing**: Supports PDF and DOCX files
- **Semantic Search**: Uses FAISS vector database for efficient similarity search
- **LLM Integration**: Powered by OpenAI GPT-4 for contextual understanding
- **RESTful API**: FastAPI-based backend with comprehensive documentation
- **Explainable AI**: Provides reasoning and confidence scores for answers
- **Token Optimization**: Efficient token usage for cost-effectiveness

## 🏗️ System Architecture

```
Input Documents (PDF/DOCX) → Document Processor → Text Chunking → 
Vector Embeddings (FAISS) → Semantic Search → LLM Processing → 
Structured JSON Response
```

## 📋 Requirements

- Python 3.8+
- OpenAI API Key
- 4GB+ RAM (for embedding models)
- Internet connection (for document download and API calls)

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd hackRX-bajaj
```

### 2. Install Dependencies and Run

```bash
python run.py
```

This will:
- Install all required dependencies
- Create necessary directories
- Set up environment configuration
- Start the FastAPI server

### 3. Configure API Key

Edit the `.env` file and add your OpenAI API key:

```env
OPENAI_API_KEY=your_actual_api_key_here
```

### 4. Access the API

- **Server**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 📡 API Usage

### Main Endpoint

**POST** `/hackrx/run`

```json
{
    "documents": "https://example.com/document.pdf",
    "questions": [
        "What is the grace period for premium payment?",
        "What is the waiting period for pre-existing diseases?"
    ]
}
```

**Response:**

```json
{
    "answers": [
        "The grace period for premium payment is 30 days.",
        "The waiting period for pre-existing diseases is 36 months."
    ]
}
```

### Authentication

Include the bearer token in your requests:

```bash
Authorization: Bearer 87e1f87355284c19bac1880413d4a1e7cb868891939fc1c6d8227ee2c1b89cb0
```

## 🧪 Testing

Run the test suite:

```bash
python test_api.py
```

This will test:
- Health check endpoint
- Document processing
- Query answering functionality

## 📁 Project Structure

```
├── main.py                 # FastAPI application
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── run.py                 # Startup script
├── test_api.py           # API testing script
├── models/
│   └── schemas.py        # Pydantic models
├── services/
│   ├── document_processor.py  # Document processing
│   ├── vector_store.py        # FAISS vector operations
│   └── query_processor.py     # Query processing & LLM
└── utils/
    └── logger.py         # Logging configuration
```

## ⚙️ Configuration

Key configuration options in `.env`:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_key_here
LLM_MODEL=gpt-4-turbo-preview

# Vector Database
EMBEDDING_MODEL=all-MiniLM-L6-v2
CHUNK_SIZE=512
CHUNK_OVERLAP=50

# API Settings
API_PORT=8000
API_BEARER_TOKEN=your_token_here
```

## 🎯 Evaluation Criteria

The system is optimized for:

- **Accuracy**: Precise query understanding and clause matching
- **Token Efficiency**: Optimized LLM usage for cost-effectiveness
- **Latency**: Fast response times with efficient vector search
- **Reusability**: Modular architecture for easy extension
- **Explainability**: Clear reasoning and source traceability

## 🔧 Advanced Usage

### Custom Document Processing

```python
from services.document_processor import DocumentProcessor

processor = DocumentProcessor()
text = await processor.process_document("document_url")
chunks = await processor.chunk_text(text)
```

### Vector Search

```python
from services.vector_store import VectorStore

vector_store = VectorStore()
await vector_store.initialize()
results = await vector_store.search("your query", top_k=5)
```

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**: Run `python run.py` to install dependencies
2. **API Key Issues**: Ensure OPENAI_API_KEY is set in `.env`
3. **Memory Issues**: Reduce CHUNK_SIZE in configuration
4. **Network Issues**: Check document URL accessibility

### Logs

Check logs in `./logs/app.log` for detailed error information.

## 📄 License

This project is developed for the HackRX competition.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

---

**Built with ❤️ for HackRX Bajaj Challenge**
